package com.logictrue.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;
import com.logictrue.util.PathUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 配置管理器，负责应用配置的持久化存储
 */
public class ConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private static final String CONFIG_FILE = "config.json";
    private static final String CONFIG_DIR = PathUtils.getApplicationDirectory();

    // 延迟保存相关配置
    private static final long SAVE_DELAY_MS = 1000; // 1秒延迟
    private static final long MAX_SAVE_DELAY_MS = 5000; // 最大5秒延迟

    private static ConfigManager instance;
    private AppConfig config;

    // 延迟保存相关字段
    private final ScheduledExecutorService saveExecutor;
    private final AtomicBoolean hasPendingChanges;
    private volatile long lastChangeTime;
    private volatile long firstChangeTime;

    // 自动保存模式控制
    private volatile boolean autoSaveEnabled = true;

    private ConfigManager() {
        this.saveExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ConfigManager-SaveThread");
            t.setDaemon(true);
            return t;
        });
        this.hasPendingChanges = new AtomicBoolean(false);
        loadConfig();

        // 启动定期检查任务
        startPeriodicSaveCheck();
    }

    public static synchronized ConfigManager getInstance() {
        if (instance == null) {
            instance = new ConfigManager();
        }
        return instance;
    }

    /**
     * 启动定期保存检查任务
     */
    private void startPeriodicSaveCheck() {
        saveExecutor.scheduleWithFixedDelay(() -> {
            if (hasPendingChanges.get()) {
                long currentTime = System.currentTimeMillis();
                // 如果距离最后一次修改超过延迟时间，或者距离第一次修改超过最大延迟时间，则保存
                if (currentTime - lastChangeTime >= SAVE_DELAY_MS ||
                    currentTime - firstChangeTime >= MAX_SAVE_DELAY_MS) {
                    doSaveConfig();
                }
            }
        }, SAVE_DELAY_MS, SAVE_DELAY_MS / 2, TimeUnit.MILLISECONDS);
    }

    /**
     * 标记配置已修改，触发延迟保存
     */
    private void markConfigChanged() {
        long currentTime = System.currentTimeMillis();
        lastChangeTime = currentTime;

        if (!hasPendingChanges.getAndSet(true)) {
            // 第一次修改，记录开始时间
            firstChangeTime = currentTime;
        }
    }

    /**
     * 执行实际的配置保存操作
     */
    private synchronized void doSaveConfig() {
        if (!hasPendingChanges.get()) {
            return;
        }

        try {
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }

            File configFile = new File(configDir, CONFIG_FILE);
            File tempFile = new File(configDir, CONFIG_FILE + ".tmp");

            // 先写入临时文件，使用格式化的JSON
            String configJson = JSON.toJSONString(config, JSONWriter.Feature.PrettyFormat);
            Files.writeString(tempFile.toPath(), configJson);

            // 验证临时文件是否完整
            if (tempFile.exists() && tempFile.length() > 0) {
                // 备份原文件（如果存在）
                if (configFile.exists()) {
                    File backupFile = new File(configDir, CONFIG_FILE + ".bak");
                    Files.copy(configFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                }

                // 将临时文件重命名为正式文件
                Files.move(tempFile.toPath(), configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                logger.info("配置文件保存成功: {}", configFile.getAbsolutePath());

                // 重置待保存标志
                hasPendingChanges.set(false);
            } else {
                logger.error("临时配置文件写入失败");
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        } catch (IOException e) {
            logger.error("保存配置文件失败", e);
        }
    }



    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }

            File configFile = new File(configDir, CONFIG_FILE);
            if (configFile.exists()) {
                String configContent = Files.readString(configFile.toPath());
                config = JSON.parseObject(configContent, AppConfig.class);
                logger.info("配置文件加载成功");
            } else {
                config = new AppConfig();
                saveConfig();
                logger.info("创建默认配置文件");
            }
        } catch (IOException e) {
            logger.error("加载配置文件失败", e);
            config = new AppConfig();
        }
    }

    /**
     * 保存配置文件（延迟保存）
     */
    public void saveConfig() {
        markConfigChanged();
    }

    /**
     * 设置自动保存模式
     * @param enabled true=自动保存，false=手动保存
     */
    public void setAutoSaveEnabled(boolean enabled) {
        this.autoSaveEnabled = enabled;
    }

    /**
     * 获取自动保存模式状态
     */
    public boolean isAutoSaveEnabled() {
        return autoSaveEnabled;
    }

    /**
     * 内部方法：根据自动保存设置决定是否触发保存
     */
    private void autoSaveIfEnabled() {
        if (autoSaveEnabled) {
            markConfigChanged();
        }
    }

    /**
     * 立即保存配置文件（强制保存）
     */
    public synchronized void saveConfigImmediately() {
        doSaveConfig();
    }

    /**
     * 批量更新配置（避免多次保存）
     */
    public void batchUpdate(Runnable updateOperations) {
        try {
            updateOperations.run();
        } finally {
            markConfigChanged();
        }
    }

    /**
     * 关闭配置管理器，保存待保存的配置并释放资源
     */
    public void shutdown() {
        if (hasPendingChanges.get()) {
            doSaveConfig();
        }
        saveExecutor.shutdown();
        try {
            if (!saveExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                saveExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            saveExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    public AppConfig getConfig() {
        return config;
    }

    public void setConfig(AppConfig config) {
        this.config = config;
        autoSaveIfEnabled();
    }

    // 便捷方法
    public String getDeviceId() {
        return config.getDeviceId();
    }

    public void setDeviceId(String deviceId) {
        config.setDeviceId(deviceId);
        autoSaveIfEnabled();
    }

    public String getFormName() {
        return config.getFormName();
    }

    public void setFormName(String formName) {
        config.setFormName(formName);
        autoSaveIfEnabled();
    }



    public String getHeartbeatUrl() {
        return config.getHeartbeatUrl();
    }

    public void setHeartbeatUrl(String heartbeatUrl) {
        config.setHeartbeatUrl(heartbeatUrl);
        autoSaveIfEnabled();
    }

    public int getHeartbeatInterval() {
        return config.getHeartbeatInterval();
    }

    public void setHeartbeatInterval(int heartbeatInterval) {
        config.setHeartbeatInterval(heartbeatInterval);
        autoSaveIfEnabled();
    }

    public String getImageUrl() {
        return config.getImageUrl();
    }

    public void setImageUrl(String imageUrl) {
        config.setImageUrl(imageUrl);
        autoSaveIfEnabled();
    }

    public String getBackgroundImagePath() {
        return config.getBackgroundImagePath();
    }

    public void setBackgroundImagePath(String backgroundImagePath) {
        config.setBackgroundImagePath(backgroundImagePath);
        autoSaveIfEnabled();
    }

    public String getCollectionPath() {
        return config.getCollectionPath();
    }

    public void setCollectionPath(String collectionPath) {
        config.setCollectionPath(collectionPath);
        autoSaveIfEnabled();
    }

    public List<FormField> getFormFields() {
        List<FormField> fields = config.getFormFields();
        logger.info("ConfigManager获取表单字段，共{}个", fields.size());
        return fields;
    }

    public void setFormFields(List<FormField> formFields) {
        config.setFormFields(formFields);
        saveConfig();
    }

    public void addFormField(FormField formField) {
        config.getFormFields().add(formField);
        saveConfig();
    }

    public void removeFormField(String fieldId) {
        config.getFormFields().removeIf(field -> field.getId().equals(fieldId));
        saveConfig();
    }

    public void updateFormField(FormField updatedField) {
        List<FormField> fields = config.getFormFields();
        for (int i = 0; i < fields.size(); i++) {
            if (fields.get(i).getId().equals(updatedField.getId())) {
                fields.set(i, updatedField);
                break;
            }
        }
        saveConfig();
    }

    // 外部应用程序管理方法
    public List<ExternalApp> getExternalApps() {
        return config.getExternalApps();
    }

    public void setExternalApps(List<ExternalApp> externalApps) {
        config.setExternalApps(externalApps);
        saveConfig();
    }

    public void addExternalApp(ExternalApp app) {
        config.getExternalApps().add(app);
        saveConfig();
    }

    public void removeExternalApp(String appId) {
        config.getExternalApps().removeIf(app -> app.getId().equals(appId));
        saveConfig();
    }

    public void updateExternalApp(ExternalApp updatedApp) {
        List<ExternalApp> apps = config.getExternalApps();
        for (int i = 0; i < apps.size(); i++) {
            if (apps.get(i).getId().equals(updatedApp.getId())) {
                apps.set(i, updatedApp);
                break;
            }
        }
        saveConfig();
    }

    // 新增的模板下载相关配置方法
    public String getTemplateDownloadUrl() {
        return config.getTemplateDownloadUrl();
    }

    public void setTemplateDownloadUrl(String templateDownloadUrl) {
        config.setTemplateDownloadUrl(templateDownloadUrl);
        saveConfig();
    }

    public String getTemplateStoragePath() {
        return config.getTemplateStoragePath();
    }

    public void setTemplateStoragePath(String templateStoragePath) {
        config.setTemplateStoragePath(templateStoragePath);
        saveConfig();
    }

    public boolean isAutoDownloadTemplate() {
        return config.isAutoDownloadTemplate();
    }

    public void setAutoDownloadTemplate(boolean autoDownloadTemplate) {
        config.setAutoDownloadTemplate(autoDownloadTemplate);
        saveConfig();
    }

    // Excel采集相关配置方法
    public String getExcelCollectionPath() {
        return config.getExcelCollectionPath();
    }

    public void setExcelCollectionPath(String excelCollectionPath) {
        config.setExcelCollectionPath(excelCollectionPath);
        saveConfig();
    }

    public boolean isAutoExcelCollection() {
        return config.isAutoExcelCollection();
    }

    public void setAutoExcelCollection(boolean autoExcelCollection) {
        config.setAutoExcelCollection(autoExcelCollection);
        saveConfig();
    }

    public int getCollectionIntervalMinutes() {
        return config.getCollectionIntervalMinutes();
    }

    public void setCollectionIntervalMinutes(int collectionIntervalMinutes) {
        config.setCollectionIntervalMinutes(collectionIntervalMinutes);
        saveConfig();
    }

    public String getLastAutoCollectionTime() {
        return config.getLastAutoCollectionTime();
    }

    public void setLastAutoCollectionTime(String lastAutoCollectionTime) {
        config.setLastAutoCollectionTime(lastAutoCollectionTime);
        saveConfig();
    }

    // 主页标题相关配置方法
    public String getMainPageTitle() {
        return config.getMainPageTitle();
    }

    public void setMainPageTitle(String mainPageTitle) {
        config.setMainPageTitle(mainPageTitle);
        saveConfig();
    }

    public double getMainPageTitleFontSize() {
        return config.getMainPageTitleFontSize();
    }

    public void setMainPageTitleFontSize(double mainPageTitleFontSize) {
        config.setMainPageTitleFontSize(mainPageTitleFontSize);
        saveConfig();
    }

    public String getMainPageTitleColor() {
        return config.getMainPageTitleColor();
    }

    public void setMainPageTitleColor(String mainPageTitleColor) {
        config.setMainPageTitleColor(mainPageTitleColor);
        saveConfig();
    }

    public double getMainPageTitleTopMargin() {
        return config.getMainPageTitleTopMargin();
    }

    public void setMainPageTitleTopMargin(double mainPageTitleTopMargin) {
        config.setMainPageTitleTopMargin(mainPageTitleTopMargin);
        saveConfig();
    }

    // 数据推送相关配置方法
    public String getPushUrl() {
        return config.getPushUrl();
    }

    public void setPushUrl(String pushUrl) {
        config.setPushUrl(pushUrl);
        saveConfig();
    }

    public boolean isAutoPushEnabled() {
        return config.isAutoPushEnabled();
    }

    public void setAutoPushEnabled(boolean autoPushEnabled) {
        config.setAutoPushEnabled(autoPushEnabled);
        saveConfig();
    }

    public int getPushRetryTimes() {
        return config.getPushRetryTimes();
    }

    public void setPushRetryTimes(int pushRetryTimes) {
        config.setPushRetryTimes(pushRetryTimes);
        saveConfig();
    }

    public int getPushTimeoutSeconds() {
        return config.getPushTimeoutSeconds();
    }

    public void setPushTimeoutSeconds(int pushTimeoutSeconds) {
        config.setPushTimeoutSeconds(pushTimeoutSeconds);
        saveConfig();
    }

    // 配置文件备份和恢复功能
    /**
     * 备份配置文件
     */
    public boolean backupConfig(String backupPath) {
        try {
            File configFile = new File(CONFIG_DIR, CONFIG_FILE);
            if (!configFile.exists()) {
                logger.warn("配置文件不存在，无法备份");
                return false;
            }

            File backupFile = new File(backupPath);
            Files.copy(configFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            logger.info("配置文件备份成功: {}", backupPath);
            return true;
        } catch (IOException e) {
            logger.error("备份配置文件失败", e);
            return false;
        }
    }

    /**
     * 自动备份配置文件（带时间戳）
     */
    public String autoBackupConfig() {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String backupFileName = "config_backup_" + timestamp + ".json";
            String backupPath = CONFIG_DIR + File.separator + backupFileName;

            if (backupConfig(backupPath)) {
                return backupPath;
            }
        } catch (Exception e) {
            logger.error("自动备份配置文件失败", e);
        }
        return null;
    }

    /**
     * 从备份文件恢复配置
     */
    public boolean restoreConfig(String backupPath) {
        try {
            File backupFile = new File(backupPath);
            if (!backupFile.exists()) {
                logger.warn("备份文件不存在: {}", backupPath);
                return false;
            }

            // 验证备份文件格式
            String backupContent = Files.readString(backupFile.toPath());
            AppConfig testConfig = JSON.parseObject(backupContent, AppConfig.class);
            if (testConfig == null) {
                logger.warn("备份文件格式无效");
                return false;
            }

            // 备份当前配置
            autoBackupConfig();

            // 恢复配置
            File configFile = new File(CONFIG_DIR, CONFIG_FILE);
            Files.copy(backupFile.toPath(), configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

            // 重新加载配置
            loadConfig();

            logger.info("配置文件恢复成功: {}", backupPath);
            return true;
        } catch (Exception e) {
            logger.error("恢复配置文件失败", e);
            return false;
        }
    }

    /**
     * 导出配置文件到指定路径
     */
    public boolean exportConfig(String exportPath) {
        return backupConfig(exportPath);
    }

    /**
     * 从指定路径导入配置文件
     */
    public boolean importConfig(String importPath) {
        return restoreConfig(importPath);
    }

    /**
     * 获取配置目录路径
     */
    public String getConfigDir() {
        return CONFIG_DIR;
    }

    /**
     * 获取配置文件路径
     */
    public String getConfigFilePath() {
        return CONFIG_DIR + File.separator + CONFIG_FILE;
    }

    // 静态块，确保应用关闭时保存配置
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (instance != null) {
                instance.shutdown();
            }
        }));
    }
}
